import { ApparelProfile, UserWardrobeInfo, ApparelCategory } from './apparel.types';
import {
  Undertone,
  UserAge,
  Gender,
  SkinTone,
  BodyType,
  Height,
  EyeColor,
  HairColor,
  Contrast,
  Season
} from './style.types';

/**
 * Standard response structure for Manychat controllers
 * This ensures consistent response format across all Manychat endpoints
 */
export interface ManychatResponse {
  status: string;
  message: string;
  data?: any;
  timestamp: string;
  errorMessage?: string;
}

export interface SetShoppableOutfitRequest {
  userId: string;
  outfit: string;
  apparelProfile: ApparelProfile;
}

export interface AnalyzeUserSelfieRequest {
  userId: string;
  imageUrl: string;
  userUndertone: Undertone;
}

/**
 * Request to create a style profile for a Manychat user
 * Required fields: userId, userGender, userUndertone
 * Other fields can be derived from profile image if provided
 */
export interface ManychatCreateStyleProfileRequest {
  userId: string;
  userGender: Gender;
  userUndertone: Undertone;
  userAge?: UserAge;
  userSkinTone?: SkinTone;
  userBodyType?: BodyType;
  userHeight?: Height;
  userHeightMetric?: string;
  userEyeColor?: EyeColor;
  userHairColor?: HairColor;
  userContrast?: Contrast;
  userSeason?: Season;
  userProfileImageUrl?: string;
  baseLocation?: string;
  manychatSubscriberId?: number; // Manychat subscriber ID for updating custom fields
}

/**
 * Request to update a style profile for a Manychat user
 */
export interface ManychatUpdateStyleProfileRequest {
  userId: string;
  userAge?: UserAge;
  userGender?: Gender;
  userUndertone?: Undertone;
  userSkinTone?: SkinTone;
  userBodyType?: BodyType;
  userHeight?: Height;
  userHeightMetric?: string;
  userEyeColor?: EyeColor;
  userHairColor?: HairColor;
  userContrast?: Contrast;
  userSeason?: Season;
  userProfileImageUrl?: string;
  baseLocation?: string;
  manychatSubscriberId?: number; // Manychat subscriber ID for updating custom fields
}

/**
 * Request to get a style profile for a Manychat user
 */
export interface ManychatGetStyleProfileRequest {
  userId: string;
}

/**
 * Request to create apparels from images for a Manychat user
 */
export interface ManychatCreateApparelRequest {
  userId: string;
  manychatSubscriberId: number;
  gender: ApparelProfile;
  imageUrls: string[];
}

/**
 * Response for apparel creation
 */
export interface ManychatCreateApparelResponse {
  success: boolean;
  message: string;
  apparels?: UserWardrobeInfo[];
  duplicates?: {
    imageUrl: string;
    apparelDescriptions: string[];
  }[];
  processedImages: number;
  successfullyProcessed: number;
  timestamp: string;
}

/**
 * Request to get apparels by category for a Manychat user
 */
export interface ManychatGetApparelsByCategoryRequest {
  userId: string;
  manychatSubscriberId: number;
  apparelCategory?: ApparelCategory; // Optional - if not provided, will return all categories
}

/**
 * Result of duplicate apparel check
 */
export interface DuplicateApparelCheckResult {
  hasDuplicates: boolean;
  duplicateDescriptions?: string[];
  uniqueDescriptions?: string[];
}

/**
 * Result of apparel extraction from image
 */
export interface ApparelExtractionResult {
  apparelDescriptions: string[];
  apparelTypes: string[];
  apparelCategories: string[];
}

/**
 * Request to set an outfit using existing items from user's wardrobe
 */
export interface ManychatSetExistingItemsOutfitRequest {
  userId: string;
  manychatSubscriberId: number;
  situationContext: ManychatSituationalContext;
}

/**
 * Request to review a user's outfit
 */
export interface ManychatReviewMyOutfitRequest {
  userId: string;
  manychatSubscriberId: number;
  imageURL: string;
  imageCaption: string;
}

/**
 * Response for reviewing a user's outfit, tailored for Manychat.
 */
export interface ManychatReviewMyOutfitResponse {
  /**
   * The textual feedback for the outfit review.
   */
  reviewFeedback: string;
  /**
   * Suggested alternative items from the user's existing wardrobe, provided as a collage image URL.
   */
  userWardrobeSuggestionsCollage?: string;
  /**
   * Suggested alternative items from the marketplace as a single string (for Manychat compatibility).
   */
  suggestedMarketplaceAlternativesString?: string;
}

/**
 * These are Manychat specific types.
 * In future, these will be deprecated to use the standard types for situation
 */
export interface ManychatSituationalContext {
  occasion: string;
  stylePreference: string;
  location?: string;
  feelsLikeWeather?: string;
  suggestedOutfit?: string; // Previously suggested outfit for iteration/refinement
  userOverride?: string; // Specific changes or constraints the user wants applied
}

/**
 * Request to generate SSO link for Manychat user
 */
export interface ManychatGetSSOLinkRequest {
  manychatSubscriberId: string;
  userId: string;
}

/**
 * Response for SSO link generation
 */
export interface ManychatGetSSOLinkResponse {
  ssoLink: string;
  success: boolean;
  message: string;
  timestamp: string;
}

/**
 * SSO Token data structure for DynamoDB storage
 */
export interface SSOTokenData {
  token: string; // Primary key - UUID token
  userId: string; // WhatsApp phone number/userId
  expiresAt: number; // Unix timestamp when token expires
  used: boolean; // Whether the token has been used
  createdAt: number; // Unix timestamp when token was created
}
