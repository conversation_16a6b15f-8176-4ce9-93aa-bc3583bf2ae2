import { SSOTokenDao } from "../db/ssoTokenDao";
import { SSOTokenData } from "../types/manychat";
import { v4 as uuidv4 } from "uuid";

export class SSOService {
  private ssoTokenDao: SSOTokenDao;

  constructor() {
    console.log("[SSOService] Initializing service");
    this.ssoTokenDao = new SSOTokenDao();
  }

  /**
   * Generate SSO link for a user
   * @param userId - The user's phone number/userId
   * @param manychatSubscriberId - The Manychat subscriber ID
   * @returns Promise<{ ssoLink: string; token: string }>
   */
  async generateSSOLink(userId: string, manychatSubscriberId: string): Promise<{ ssoLink: string; token: string }> {
    console.log("[SSOService][generateSSOLink] Generating SSO link for user:", {
      userId,
      manychatSubscriberId
    });

    try {
      // Generate secure UUID token
      const token = uuidv4();
      console.log("[SSOService][generateSSOLink] Generated UUID token:", token);

      // Set token expiration to 5 minutes from now
      const currentTime = Math.floor(Date.now() / 1000);
      const expirationTime = currentTime + (5 * 60); // 5 minutes in seconds

      // Create token data
      const tokenData: SSOTokenData = {
        token,
        userId,
        expiresAt: expirationTime,
        used: false,
        createdAt: currentTime
      };

      // Store token in DynamoDB
      await this.ssoTokenDao.createSSOToken(tokenData);

      // Generate SSO link with subscriberId
      const ssoLink = `web.monova.in/login?token=${token}&subscriberId=${manychatSubscriberId}`;

      console.log("[SSOService][generateSSOLink] SSO link generated successfully:", {
        token,
        userId,
        ssoLink,
        expiresAt: expirationTime
      });

      return { ssoLink, token };
    } catch (error) {
      console.error("[SSOService][generateSSOLink] Error generating SSO link:", error);
      throw new Error(`Failed to generate SSO link: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Validate SSO token
   * @param token - The SSO token to validate
   * @returns Promise<{ valid: boolean; userId?: string; reason?: string }>
   */
  async validateSSOToken(token: string): Promise<{ valid: boolean; userId?: string; reason?: string }> {
    console.log("[SSOService][validateSSOToken] Validating SSO token:", token);

    try {
      const validationResult = await this.ssoTokenDao.isTokenValid(token);

      if (!validationResult.valid) {
        console.log("[SSOService][validateSSOToken] Token validation failed:", validationResult.reason);
        return {
          valid: false,
          reason: validationResult.reason
        };
      }

      console.log("[SSOService][validateSSOToken] Token is valid for user:", validationResult.tokenData?.userId);
      return {
        valid: true,
        userId: validationResult.tokenData?.userId
      };
    } catch (error) {
      console.error("[SSOService][validateSSOToken] Error validating token:", error);
      return {
        valid: false,
        reason: "Error validating token"
      };
    }
  }

  /**
   * Use SSO token (mark as used and return user info)
   * @param token - The SSO token to use
   * @returns Promise<{ success: boolean; userId?: string; reason?: string }>
   */
  async useSSOToken(token: string): Promise<{ success: boolean; userId?: string; reason?: string }> {
    console.log("[SSOService][useSSOToken] Using SSO token:", token);

    try {
      // First validate the token
      const validationResult = await this.validateSSOToken(token);

      if (!validationResult.valid) {
        return {
          success: false,
          reason: validationResult.reason
        };
      }

      // Mark token as used
      await this.ssoTokenDao.markTokenAsUsed(token);

      console.log("[SSOService][useSSOToken] Token used successfully for user:", validationResult.userId);
      return {
        success: true,
        userId: validationResult.userId
      };
    } catch (error) {
      console.error("[SSOService][useSSOToken] Error using token:", error);
      return {
        success: false,
        reason: "Error using token"
      };
    }
  }
}
